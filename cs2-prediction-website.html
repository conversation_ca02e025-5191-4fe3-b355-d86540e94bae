<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2 Predictor - AI Match Predictions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            padding: 20px 0;
            border-bottom: 1px solid #333;
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(10, 10, 10, 0.8);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-tier {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .tier-free {
            background: linear-gradient(45deg, #666, #999);
            color: white;
        }

        .tier-pro {
            background: linear-gradient(45deg, #00ff88, #0099ff);
            color: white;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .upgrade-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            border: none;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
        }

        /* Main Content */
        .main-content {
            padding: 40px 0;
        }

        .hero {
            text-align: center;
            margin-bottom: 60px;
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero p {
            font-size: 18px;
            color: #ccc;
            margin-bottom: 30px;
        }

        /* Predictions Section */
        .predictions-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 28px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 28px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            border-radius: 2px;
        }

        .predictions-grid {
            display: grid;
            gap: 20px;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        }

        .prediction-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .prediction-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #00ff88, #0099ff);
        }

        .prediction-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 255, 136, 0.1);
            border-color: rgba(0, 255, 136, 0.3);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .match-time {
            font-size: 12px;
            color: #888;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
        }

        .match-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 8px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            color: white;
        }

        .teams {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .team {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
        }

        .team.winner {
            transform: scale(1.05);
        }

        .team.winner .team-logo {
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
            border: 2px solid #00ff88;
        }

        .winner-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            color: white;
            font-size: 10px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4);
        }

        .team-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #333, #555);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 20px;
        }

        .team-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .win-percentage {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .win-percentage.winner {
            font-size: 28px;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .vs {
            font-size: 20px;
            color: #666;
            font-weight: bold;
        }

        .prediction-details {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .prediction-factors {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .factor-tag {
            padding: 4px 8px;
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 12px;
            font-size: 12px;
            color: #00ff88;
        }

        .confidence-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #0099ff);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .confidence-label {
            font-size: 12px;
            color: #ccc;
        }

        /* Blurred/Locked Cards */
        .prediction-card.locked {
            position: relative;
        }

        .prediction-card.locked .teams,
        .prediction-card.locked .prediction-details {
            filter: blur(8px);
            pointer-events: none;
        }

        .unlock-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .unlock-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 15px;
            background: linear-gradient(45deg, #ff006e, #8338ec);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .unlock-text {
            color: #fff;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .unlock-btn {
            padding: 8px 16px;
            background: linear-gradient(45deg, #00f5ff, #ff006e);
            border: none;
            border-radius: 20px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .unlock-btn:hover {
            transform: scale(1.05);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 36px;
            }
            
            .predictions-grid {
                grid-template-columns: 1fr;
            }
            
            .nav {
                flex-direction: column;
                gap: 20px;
            }
        }

        /* Loading Animation */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <nav class="nav">
                <div class="logo">CS2 Predictor</div>
                <div class="user-info">
                    <div class="user-tier tier-free" id="userTier">Free Tier</div>
                    <button class="upgrade-btn" id="upgradeBtn">Upgrade to Pro</button>
                </div>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <section class="hero">
                <h1>AI-Powered CS2 Predictions</h1>
                <p>Get accurate match predictions powered by advanced machine learning algorithms</p>
            </section>

            <section class="predictions-section">
                <h2 class="section-title">Today's Matches</h2>
                <div class="predictions-grid" id="predictionsGrid">
                    <!-- Predictions will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <script>
        // Mock data for predictions
        const mockPredictions = [
            {
                id: 1,
                team1: { name: "FaZe Clan", logo: "FZ", winChance: 67 },
                team2: { name: "NAVI", logo: "NV", winChance: 33 },
                time: "14:30 CET",
                status: "LIVE",
                factors: ["Recent Form", "Head-to-Head", "Map Pool", "Player Stats"],
                confidence: 85,
                isFree: true
            },
            {
                id: 2,
                team1: { name: "Astralis", logo: "AST", winChance: 54 },
                team2: { name: "G2 Esports", logo: "G2", winChance: 46 },
                time: "16:00 CET",
                status: "Upcoming",
                factors: ["Map Advantage", "Recent Performance", "Tournament Form"],
                confidence: 72,
                isFree: true
            },
            {
                id: 3,
                team1: { name: "Liquid", logo: "TL", winChance: 71 },
                team2: { name: "Vitality", logo: "VIT", winChance: 29 },
                time: "18:30 CET",
                status: "Upcoming",
                factors: ["Individual Skill", "Team Chemistry", "Recent Wins"],
                confidence: 91,
                isFree: true
            },
            {
                id: 4,
                team1: { name: "NIP", logo: "NIP", winChance: 58 },
                team2: { name: "BIG", logo: "BIG", winChance: 42 },
                time: "20:00 CET",
                status: "Upcoming",
                factors: ["Map Selection", "Player Form", "Historical Data"],
                confidence: 68,
                isFree: false
            },
            {
                id: 5,
                team1: { name: "Cloud9", logo: "C9", winChance: 63 },
                team2: { name: "FURIA", logo: "FUR", winChance: 37 },
                time: "22:15 CET",
                status: "Upcoming",
                factors: ["Regional Strength", "Playstyle Match", "Recent Results"],
                confidence: 79,
                isFree: false
            },
            {
                id: 6,
                team1: { name: "ENCE", logo: "ENC", winChance: 49 },
                team2: { name: "OG", logo: "OG", winChance: 51 },
                time: "23:45 CET",
                status: "Upcoming",
                factors: ["Close Match", "Form Analysis", "Map Preferences"],
                confidence: 61,
                isFree: false
            }
        ];

        let userTier = 'free'; // Can be 'free' or 'pro'

        function renderPredictions() {
            const grid = document.getElementById('predictionsGrid');
            const freeCount = mockPredictions.filter(p => p.isFree).length;
            
            grid.innerHTML = mockPredictions.map((prediction, index) => {
                const isLocked = userTier === 'free' && !prediction.isFree;
                
                return `
                    <div class="prediction-card ${isLocked ? 'locked' : ''}">
                        <div class="match-header">
                            <span class="match-time">${prediction.time}</span>
                            <span class="match-status">${prediction.status}</span>
                        </div>
                        
                        <div class="teams">
                            <div class="team ${prediction.team1.winChance > prediction.team2.winChance ? 'winner' : ''}">
                                <div class="team-logo">${prediction.team1.logo}</div>
                                <div class="team-name">${prediction.team1.name}</div>
                                ${!isLocked || userTier === 'pro' ? `<div class="win-percentage ${prediction.team1.winChance > prediction.team2.winChance ? 'winner' : ''}">${prediction.team1.winChance}%</div>` : ''}
                                ${!isLocked && prediction.team1.winChance > prediction.team2.winChance ? '<div class="winner-badge">Winner</div>' : ''}
                            </div>
                            <div class="vs">VS</div>
                            <div class="team ${prediction.team2.winChance > prediction.team1.winChance ? 'winner' : ''}">
                                <div class="team-logo">${prediction.team2.logo}</div>
                                <div class="team-name">${prediction.team2.name}</div>
                                ${!isLocked || userTier === 'pro' ? `<div class="win-percentage ${prediction.team2.winChance > prediction.team1.winChance ? 'winner' : ''}">${prediction.team2.winChance}%</div>` : ''}
                                ${!isLocked && prediction.team2.winChance > prediction.team1.winChance ? '<div class="winner-badge">Winner</div>' : ''}
                            </div>
                        </div>

                        ${!isLocked || userTier === 'pro' ? `
                            <div class="prediction-details">
                                <div class="prediction-factors">
                                    ${prediction.factors.map(factor => `<span class="factor-tag">${factor}</span>`).join('')}
                                </div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: ${prediction.confidence}%"></div>
                                </div>
                                <div class="confidence-label">Confidence: ${prediction.confidence}%</div>
                            </div>
                        ` : ''}

                        ${isLocked ? `
                            <div class="unlock-overlay">
                                <div class="unlock-icon">🔒</div>
                                <div class="unlock-text">Pro Feature</div>
                                <button class="unlock-btn" onclick="upgradeToPro()">Upgrade Now</button>
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        function upgradeToPro() {
            if (userTier === 'free') {
                userTier = 'pro';
                const tierElement = document.getElementById('userTier');
                const upgradeBtn = document.getElementById('upgradeBtn');
                
                tierElement.textContent = 'Pro Tier';
                tierElement.className = 'user-tier tier-pro';
                upgradeBtn.textContent = 'Manage Subscription';
                
                renderPredictions();
            }
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            renderPredictions();
            
            document.getElementById('upgradeBtn').addEventListener('click', upgradeToPro);
        });

        // Simulate real-time updates
        setInterval(() => {
            // Update confidence levels slightly for demo
            mockPredictions.forEach(prediction => {
                const variation = Math.floor(Math.random() * 3) - 1;
                prediction.confidence = Math.max(50, Math.min(95, prediction.confidence + variation));
            });
            
            if (userTier === 'pro') {
                renderPredictions();
            }
        }, 5000);
    </script>
</body>
</html>