<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2 Predictor - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            padding: 0 20px;
            position: relative;
        }

        /* Background Effects */
        .bg-effects {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(0, 255, 136, 0.1), rgba(0, 153, 255, 0.1));
            animation: float 6s ease-in-out infinite;
        }

        .bg-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-circle:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }

        .bg-circle:nth-child(3) {
            width: 100px;
            height: 100px;
            top: 40%;
            right: 30%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-20px) scale(1.1); }
        }

        /* Logo Header */
        .logo-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .tagline {
            color: #888;
            font-size: 16px;
        }

        /* Login Container */
        .login-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 50px 40px;
            backdrop-filter: blur(20px);
            max-width: 450px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00ff88, #0099ff);
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }

        .login-subtitle {
            color: #aaa;
            font-size: 16px;
        }

        /* Form Styles */
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-group {
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #ccc;
            font-size: 14px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: #fff;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .form-input::placeholder {
            color: #666;
        }

        /* Password Toggle */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            font-size: 18px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #00ff88;
        }

        /* Remember Me & Forgot Password */
        .form-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            accent-color: #00ff88;
        }

        .checkbox-label {
            color: #ccc;
            font-size: 14px;
            user-select: none;
            cursor: pointer;
        }

        .forgot-link {
            color: #00ff88;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-link:hover {
            color: #0099ff;
            text-decoration: underline;
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            padding: 15px 20px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 255, 136, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Loading State */
        .login-btn.loading {
            position: relative;
            color: transparent;
        }

        .login-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Divider */
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #666;
            font-size: 14px;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1;
        }

        .divider span {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
            padding: 0 20px;
            position: relative;
            z-index: 2;
        }

        /* Social Login */
        .social-login {
            display: flex;
            gap: 15px;
        }

        .social-btn {
            flex: 1;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: #fff;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .social-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* Sign Up Link */
        .signup-link {
            text-align: center;
            margin-top: 30px;
            color: #aaa;
            font-size: 14px;
        }

        .signup-link a {
            color: #00ff88;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }

        .signup-link a:hover {
            color: #0099ff;
            text-decoration: underline;
        }

        /* Error Message */
        .error-message {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 8px;
            padding: 12px 15px;
            color: #ff6b6b;
            font-size: 14px;
            margin-bottom: 20px;
            display: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .login-container {
                padding: 40px 30px;
            }
            
            .logo {
                font-size: 28px;
            }
            
            .login-title {
                font-size: 24px;
            }
            
            .social-login {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="bg-effects">
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
    </div>

    <div class="container">
        <div class="logo-header">
            <div class="logo">CS2 Predictor</div>
            <div class="tagline">AI-Powered Match Predictions</div>
        </div>

        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">Welcome Back</h1>
                <p class="login-subtitle">Sign in to access your predictions</p>
            </div>

            <div class="error-message" id="errorMessage">
                Invalid email or password. Please try again.
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label class="form-label" for="email">Email Address</label>
                    <input 
                        type="email" 
                        id="email" 
                        class="form-input" 
                        placeholder="Enter your email" 
                        required
                        autocomplete="email"
                    >
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <div style="position: relative;">
                        <input 
                            type="password" 
                            id="password" 
                            class="form-input" 
                            placeholder="Enter your password" 
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            👁️
                        </button>
                    </div>
                </div>

                <div class="form-row">
                    <div class="checkbox-group">
                        <input type="checkbox" id="remember" class="checkbox">
                        <label for="remember" class="checkbox-label">Remember me</label>
                    </div>
                    <a href="#" class="forgot-link" onclick="showForgotPassword()">Forgot Password?</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    Sign In
                </button>
            </form>

            <div class="divider">
                <span>or continue with</span>
            </div>

            <div class="social-login">
                <a href="#" class="social-btn" onclick="socialLogin('google')">
                    <span>🔍</span>
                    Google
                </a>
                <a href="#" class="social-btn" onclick="socialLogin('steam')">
                    <span>🎮</span>
                    Steam
                </a>
            </div>

            <div class="signup-link">
                Don't have an account? <a href="#" onclick="showSignup()">Sign up here</a>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;

        // Mock user credentials for demo
        const mockCredentials = {
            email: '<EMAIL>',
            password: 'password123'
        };

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (isLoading) return;
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            const loginBtn = document.getElementById('loginBtn');
            
            // Hide previous error
            errorMessage.style.display = 'none';
            
            // Start loading
            isLoading = true;
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                if (email === mockCredentials.email && password === mockCredentials.password) {
                    // Success - redirect to main app
                    alert('Login successful! Redirecting to dashboard...');
                    // In real app: window.location.href = '/dashboard';
                } else {
                    // Show error
                    errorMessage.style.display = 'block';
                    
                    // Add shake animation to form
                    const container = document.querySelector('.login-container');
                    container.style.animation = 'shake 0.5s ease-in-out';
                    setTimeout(() => {
                        container.style.animation = '';
                    }, 500);
                }
                
                // Stop loading
                isLoading = false;
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
            }, 1500);
        });

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggle = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggle.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggle.textContent = '👁️';
            }
        }

        // Social login handlers
        function socialLogin(provider) {
            alert(`${provider} login would be integrated here. For demo, use:\nEmail: <EMAIL>\nPassword: password123`);
        }

        // Forgot password handler
        function showForgotPassword() {
            const email = prompt('Enter your email address to reset password:');
            if (email) {
                alert(`Password reset email sent to ${email} (demo mode)`);
            }
        }

        // Sign up handler  
        function showSignup() {
            alert('Sign up page would be shown here');
        }

        // Add shake animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-10px); }
                75% { transform: translateX(10px); }
            }
        `;
        document.head.appendChild(style);

        // Demo hint
        window.addEventListener('load', function() {
            setTimeout(() => {
                const hint = document.createElement('div');
                hint.innerHTML = `
                    <div style="position: fixed; top: 20px; right: 20px; background: rgba(0, 255, 136, 0.1); border: 1px solid rgba(0, 255, 136, 0.3); padding: 15px; border-radius: 8px; font-size: 12px; color: #00ff88; max-width: 250px; z-index: 1000;">
                        <strong>Demo Login:</strong><br>
                        Email: <EMAIL><br>
                        Password: password123
                    </div>
                `;
                document.body.appendChild(hint);
                
                // Auto-hide after 5 seconds
                setTimeout(() => {
                    hint.style.opacity = '0';
                    hint.style.transition = 'opacity 0.5s ease';
                    setTimeout(() => hint.remove(), 500);
                }, 5000);
            }, 1000);
        });
    </script>
</body>
</html>