<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2 Predictor - Crypto Payment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #0a0a0a 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            padding: 20px 0;
            border-bottom: 1px solid #333;
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(10, 10, 10, 0.8);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            cursor: pointer;
        }

        .back-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Main Content */
        .main-content {
            padding: 40px 0;
            max-width: 800px;
            margin: 0 auto;
        }

        .payment-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .payment-title {
            font-size: 36px;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .payment-subtitle {
            color: #aaa;
            font-size: 16px;
        }

        /* Plan Selection */
        .plan-selector {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }

        .plan-selector h3 {
            color: #fff;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .plan-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .plan-option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .plan-option:hover {
            border-color: rgba(0, 255, 136, 0.3);
            transform: translateY(-2px);
        }

        .plan-option.selected {
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);
        }

        .plan-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #fff;
        }

        .plan-price {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }

        .plan-period {
            font-size: 12px;
            color: #888;
        }

        .plan-savings {
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-top: 5px;
            display: inline-block;
        }

        /* Crypto Payment Section */
        .crypto-payment {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }

        .crypto-payment h3 {
            color: #fff;
            margin-bottom: 25px;
            font-size: 20px;
        }

        .crypto-networks {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .network-option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .network-option:hover {
            border-color: rgba(0, 255, 136, 0.3);
            transform: translateY(-2px);
        }

        .network-option.selected {
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);
        }

        .network-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .network-name {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }

        .network-fee {
            font-size: 12px;
            color: #888;
        }

        /* Payment Details */
        .payment-details {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
            display: none;
        }

        .payment-details.show {
            display: block;
        }

        .payment-info {
            display: grid;
            gap: 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #aaa;
            font-size: 14px;
        }

        .info-value {
            color: #fff;
            font-weight: bold;
            font-size: 16px;
        }

        .crypto-amount {
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 18px;
        }

        /* Wallet Address */
        .wallet-section {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .wallet-label {
            color: #00ff88;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .wallet-address {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: #fff;
            font-family: monospace;
            font-size: 14px;
            word-break: break-all;
            margin-bottom: 10px;
        }

        .copy-btn {
            background: linear-gradient(45deg, #00ff88, #0099ff);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }

        .copy-btn.copied {
            background: #00ff88;
        }

        /* QR Code */
        .qr-section {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            background: #fff;
            border-radius: 12px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #333;
        }

        /* Status Messages */
        .status-message {
            padding: 15px 20px;
            border-radius: 12px;
            margin: 20px 0;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            display: none;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #ffc107;
        }

        .status-success {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.3);
            color: #00ff88;
        }

        .status-error {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            color: #ff6b6b;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .action-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .primary-btn {
            background: linear-gradient(45deg, #00ff88, #0099ff);
            color: white;
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 255, 136, 0.4);
        }

        .secondary-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
        }

        .secondary-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Timer */
        .timer {
            text-align: center;
            margin: 20px 0;
            color: #ffc107;
            font-size: 18px;
            font-weight: bold;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .plan-options {
                grid-template-columns: 1fr;
            }
            
            .crypto-networks {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .nav {
                flex-direction: column;
                gap: 15px;
            }
            
            .payment-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <nav class="nav">
                <div class="logo" onclick="goBack()">CS2 Predictor</div>
                <a href="#" class="back-btn" onclick="goBack()">← Back to Plans</a>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="payment-header">
                <h1 class="payment-title">Crypto Payment</h1>
                <p class="payment-subtitle">Pay with cryptocurrency for instant activation</p>
            </div>

            <!-- Plan Selection -->
            <div class="plan-selector">
                <h3>Select Your Plan</h3>
                <div class="plan-options">
                    <div class="plan-option selected" data-plan="monthly" data-price="15">
                        <div class="plan-name">Monthly Pro</div>
                        <div class="plan-price">$15</div>
                        <div class="plan-period">per month</div>
                    </div>
                    <div class="plan-option" data-plan="yearly" data-price="50">
                        <div class="plan-name">Yearly Pro</div>
                        <div class="plan-price">$50</div>
                        <div class="plan-period">per year</div>
                        <div class="plan-savings">Save $130</div>
                    </div>
                    <div class="plan-option" data-plan="lifetime" data-price="100">
                        <div class="plan-name">Lifetime Pro</div>
                        <div class="plan-price">$100</div>
                        <div class="plan-period">one-time</div>
                        <div class="plan-savings">Best Value</div>
                    </div>
                </div>
            </div>

            <!-- Crypto Network Selection -->
            <div class="crypto-payment">
                <h3>Choose Blockchain Network</h3>
                <div class="crypto-networks">
                    <div class="network-option selected" data-network="eth" data-symbol="ETH">
                        <div class="network-icon">⟠</div>
                        <div class="network-name">Ethereum</div>
                        <div class="network-fee">~$15 fee</div>
                    </div>
                    <div class="network-option" data-network="bnb" data-symbol="BNB">
                        <div class="network-icon">🟡</div>
                        <div class="network-name">BNB Chain</div>
                        <div class="network-fee">~$0.50 fee</div>
                    </div>
                    <div class="network-option" data-network="polygon" data-symbol="MATIC">
                        <div class="network-icon">🟣</div>
                        <div class="network-name">Polygon</div>
                        <div class="network-fee">~$0.01 fee</div>
                    </div>
                    <div class="network-option" data-network="arbitrum" data-symbol="ETH">
                        <div class="network-icon">🔵</div>
                        <div class="network-name">Arbitrum</div>
                        <div class="network-fee">~$0.50 fee</div>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="payment-details show" id="paymentDetails">
                <div class="payment-info">
                    <div class="info-row">
                        <span class="info-label">Plan:</span>
                        <span class="info-value" id="selectedPlan">Monthly Pro</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">USD Amount:</span>
                        <span class="info-value" id="usdAmount">$15.00</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Network:</span>
                        <span class="info-value" id="selectedNetwork">Ethereum</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Amount to Send:</span>
                        <span class="info-value crypto-amount" id="cryptoAmount">0.0089 ETH</span>
                    </div>
                </div>

                <div class="wallet-section">
                    <div class="wallet-label">Send to this address:</div>
                    <div class="wallet-address" id="walletAddress">0x742d35Cc6Bf4c5fA9C4c8F5E8C9F16bD4F8E3d2A1</div>
                    <button class="copy-btn" onclick="copyAddress()">Copy Address</button>
                </div>

                <div class="qr-section">
                    <div class="qr-code">QR Code would appear here</div>
                    <p style="color: #888; font-size: 12px;">Scan with your crypto wallet</p>
                </div>

                <div class="timer" id="paymentTimer">Payment expires in: 14:59</div>

                <div class="status-message status-pending" id="statusMessage">
                    Waiting for payment... Please send the exact amount to the address above.
                </div>
            </div>

            <div class="action-buttons">
                <button class="secondary-btn" onclick="checkPayment()">Check Payment Status</button>
                <button class="primary-btn" onclick="simulatePayment()">Simulate Payment (Demo)</button>
            </div>
        </main>
    </div>

    <script>
        let selectedPlan = 'monthly';
        let selectedNetwork = 'eth';
        let paymentTimer;
        let timeLeft = 900; // 15 minutes

        // Mock crypto prices (in real app, fetch from API)
        const cryptoPrices = {
            eth: 2800,
            bnb: 350,
            matic: 0.85,
            arbitrum: 2800 // Uses ETH on Arbitrum
        };

        // Mock wallet addresses (in real app, generate unique addresses)
        const walletAddresses = {
            eth: '0x742d35Cc6Bf4c5fA9C4c8F5E8C9F16bD4F8E3d2A1',
            bnb: '******************************************',
            polygon: '******************************************',
            arbitrum: '******************************************'
        };

        // Plan selection
        document.querySelectorAll('.plan-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.plan-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
                selectedPlan = this.dataset.plan;
                updatePaymentDetails();
            });
        });

        // Network selection
        document.querySelectorAll('.network-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.network-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
                selectedNetwork = this.dataset.network;
                updatePaymentDetails();
            });
        });

        // Update payment details
        function updatePaymentDetails() {
            const planData = {
                monthly: { name: 'Monthly Pro', price: 15 },
                yearly: { name: 'Yearly Pro', price: 50 },
                lifetime: { name: 'Lifetime Pro', price: 100 }
            };

            const networkData = {
                eth: 'Ethereum',
                bnb: 'BNB Chain',
                polygon: 'Polygon',
                arbitrum: 'Arbitrum'
            };

            const selectedPlanData = planData[selectedPlan];
            const usdAmount = selectedPlanData.price;
            const cryptoPrice = cryptoPrices[selectedNetwork];
            const cryptoAmount = (usdAmount / cryptoPrice).toFixed(6);
            const symbol = document.querySelector(`[data-network="${selectedNetwork}"]`).dataset.symbol;

            document.getElementById('selectedPlan').textContent = selectedPlanData.name;
            document.getElementById('usdAmount').textContent = `$${usdAmount}.00`;
            document.getElementById('selectedNetwork').textContent = networkData[selectedNetwork];
            document.getElementById('cryptoAmount').textContent = `${cryptoAmount} ${symbol}`;
            document.getElementById('walletAddress').textContent = walletAddresses[selectedNetwork];

            // Reset timer
            timeLeft = 900;
            startTimer();
        }

        // Start payment timer
        function startTimer() {
            if (paymentTimer) clearInterval(paymentTimer);
            
            paymentTimer = setInterval(() => {
                timeLeft--;
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                document.getElementById('paymentTimer').textContent = 
                    `Payment expires in: ${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                if (timeLeft <= 0) {
                    clearInterval(paymentTimer);
                    showStatus('error', 'Payment expired. Please try again.');
                }
            }, 1000);
        }

        // Copy wallet address
        function copyAddress() {
            const address = document.getElementById('walletAddress').textContent;
            navigator.clipboard.writeText(address).then(() => {
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.classList.add('copied');
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('copied');
                }, 2000);
            });
        }

        // Show status message
        function showStatus(type, message) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.className = `status-message status-${type}`;
            statusEl.textContent = message;
            statusEl.style.display = 'block';
        }

        // Check payment status
        function checkPayment() {
            showStatus('pending', 'Checking blockchain for payment...');
            
            setTimeout(() => {
                // In real app, check blockchain API
                showStatus('pending', 'No payment detected yet. Please ensure you sent the exact amount.');
            }, 2000);
        }

        // Simulate successful payment (for demo)
        function simulatePayment() {
            showStatus('pending', 'Processing payment...');
            
            setTimeout(() => {
                showStatus('success', 'Payment confirmed! Your Pro subscription is now active. Redirecting...');
                clearInterval(paymentTimer);
                
                setTimeout(() => {
                    alert('Payment successful! Welcome to CS2 Predictor Pro!');
                    // In real app: window.location.href = '/dashboard';
                }, 2000);
            }, 3000);
        }

        // Go back function
        function goBack() {
            window.history.back();
        }

        // Initialize
        updatePaymentDetails();
        showStatus('pending', 'Waiting for payment... Please send the exact amount to the address above.');
    </script>
</body>
</html>